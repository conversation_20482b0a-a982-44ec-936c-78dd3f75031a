/**
 * 🔧 S3通用客户端 - 极限精简版
 * 
 * 使用AWS SDK实现标准S3协议，兼容七牛云、阿里云、腾讯云等
 * 支持上传、下载、列表、删除等基本操作
 * 
 * 特性:
 * - 标准S3 API，兼容性最佳
 * - 自动重试和错误处理
 * - 支持流式上传下载
 * - 极简接口设计
 */

const { S3Client, PutObjectCommand, GetObjectCommand, ListObjectsV2Command, DeleteObjectCommand } = require('@aws-sdk/client-s3');

class S3Manager {
    constructor(config) {
        this.config = config;
        this.client = new S3Client({
            region: config.region || 'us-east-1',
            endpoint: config.endpoint,
            credentials: {
                accessKeyId: config.accessKey,
                secretAccessKey: config.secretKey
            },
            forcePathStyle: true // 七牛云需要使用path-style
        });
    }

    // 上传文件
    async upload(key, data) {
        try {
            const command = new PutObjectCommand({
                Bucket: this.config.bucket,
                Key: key,
                Body: data,
                ContentType: 'application/json'
            });
            
            const result = await this.client.send(command);
            return { success: true, etag: result.ETag };
        } catch (error) {
            throw new Error(`S3上传失败: ${error.message}`);
        }
    }

    // 下载文件
    async download(key) {
        try {
            const command = new GetObjectCommand({
                Bucket: this.config.bucket,
                Key: key
            });
            
            const result = await this.client.send(command);
            const chunks = [];
            
            for await (const chunk of result.Body) {
                chunks.push(chunk);
            }
            
            return Buffer.concat(chunks).toString('utf-8');
        } catch (error) {
            throw new Error(`S3下载失败: ${error.message}`);
        }
    }

    // 列出文件
    async list(prefix = '') {
        try {
            const command = new ListObjectsV2Command({
                Bucket: this.config.bucket,
                Prefix: prefix,
                MaxKeys: 1000
            });
            
            const result = await this.client.send(command);
            return result.Contents || [];
        } catch (error) {
            throw new Error(`S3列表失败: ${error.message}`);
        }
    }

    // 删除文件
    async delete(key) {
        try {
            const command = new DeleteObjectCommand({
                Bucket: this.config.bucket,
                Key: key
            });
            
            await this.client.send(command);
            return { success: true };
        } catch (error) {
            throw new Error(`S3删除失败: ${error.message}`);
        }
    }

    // 检查文件是否存在
    async exists(key) {
        try {
            const command = new GetObjectCommand({
                Bucket: this.config.bucket,
                Key: key
            });
            
            await this.client.send(command);
            return true;
        } catch (error) {
            if (error.name === 'NoSuchKey') return false;
            throw error;
        }
    }
}

module.exports = S3Manager;
