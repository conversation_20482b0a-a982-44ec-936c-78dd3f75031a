/**
 * 🔧 S3存储配置管理 - 通用版
 * 
 * 管理S3兼容存储的配置信息，支持多种云存储提供商
 * 包括七牛云、阿里云、腾讯云、AWS S3、MinIO等
 * 
 * 特性:
 * - 通用S3协议支持
 * - 多云存储提供商
 * - 安全的配置存储
 * - 自动配置验证
 * - 简洁的API接口
 */

const fs = require('fs');
const path = require('path');

class S3Config {
    constructor() {
        this.configFile = path.join(__dirname, 's3-config.json');
        this.defaultConfig = {
            provider: 'qiniu',
            accessKey: 'kFCOCF3QVYUcXHk75A5SC9VLPkYzVZZHPq2oApnk',
            secretKey: 'w5YsfJLlrJ3FJdafTq35ht5JYcc38svuDS3YI2Xp',
            bucket: 'siyuan-mediaplayer',
            region: 'cn-south-1',
            endpoint: 'https://s3.cn-south-1.qiniucs.com',
            encryptionKey: 'SiYuan_Fixed_Master_Key_2024_Secure_Change_This'
        };
        this.config = this.loadConfig();
        this.updateFromEnv();
    }

    // 加载配置
    loadConfig() {
        try {
            if (fs.existsSync(this.configFile)) {
                const data = fs.readFileSync(this.configFile, 'utf8');
                return { ...this.defaultConfig, ...JSON.parse(data) };
            }
        } catch (error) {
            console.warn('配置文件读取失败，使用默认配置:', error.message);
        }
        return { ...this.defaultConfig };
    }

    // 保存配置
    saveConfig() {
        try {
            fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
            return true;
        } catch (error) {
            console.error('配置文件保存失败:', error.message);
            return false;
        }
    }

    // 获取配置
    get() {
        return { ...this.config };
    }

    // 获取原始配置（用于兼容）
    getRaw() {
        return this.config;
    }

    // 更新配置
    update(newConfig) {
        this.config = { ...this.config, ...newConfig };
        return this.saveConfig();
    }

    // 验证配置
    validate() {
        const required = ['accessKey', 'secretKey', 'bucket', 'region', 'endpoint'];
        const missing = required.filter(key => !this.config[key]);
        
        if (missing.length > 0) {
            return { valid: false, message: `缺少必需配置: ${missing.join(', ')}` };
        }
        
        return { valid: true, message: '配置验证通过' };
    }

    // 获取预设配置
    getPresets() {
        return {
            qiniu: {
                name: '七牛云',
                endpoint: 'https://s3.cn-south-1.qiniucs.com',
                region: 'cn-south-1'
            },
            aliyun: {
                name: '阿里云OSS',
                endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
                region: 'cn-hangzhou'
            },
            tencent: {
                name: '腾讯云COS',
                endpoint: 'https://cos.ap-beijing.myqcloud.com',
                region: 'ap-beijing'
            },
            aws: {
                name: 'AWS S3',
                endpoint: 'https://s3.amazonaws.com',
                region: 'us-east-1'
            },
            minio: {
                name: 'MinIO',
                endpoint: 'http://localhost:9000',
                region: 'us-east-1'
            }
        };
    }

    // 应用预设配置
    applyPreset(provider) {
        const presets = this.getPresets();
        if (presets[provider]) {
            this.config.provider = provider;
            this.config.endpoint = presets[provider].endpoint;
            this.config.region = presets[provider].region;
            return this.saveConfig();
        }
        return false;
    }

    // 从环境变量更新配置
    updateFromEnv() {
        const envMapping = {
            S3_ACCESS_KEY: 'accessKey',
            S3_SECRET_KEY: 'secretKey',
            S3_BUCKET: 'bucket',
            S3_REGION: 'region',
            S3_ENDPOINT: 'endpoint',
            ENCRYPTION_KEY: 'encryptionKey',
            // 兼容旧的七牛云环境变量
            QINIU_ACCESS_KEY: 'accessKey',
            QINIU_SECRET_KEY: 'secretKey',
            QINIU_BUCKET: 'bucket'
        };

        let updated = false;
        Object.entries(envMapping).forEach(([envKey, configKey]) => {
            if (process.env[envKey]) {
                this.config[configKey] = process.env[envKey];
                updated = true;
            }
        });

        if (updated) {
            console.log('✅ 已从环境变量更新S3配置');
        }
    }

    // 获取加密配置
    getEncryptionConfig() {
        return {
            key: this.config.encryptionKey,
            algorithm: 'aes-256-cbc'
        };
    }

    // 获取SDK配置（向后兼容）
    getSDKConfig() {
        return this.get();
    }
}

module.exports = new S3Config();
