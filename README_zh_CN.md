<div class="sy__outline" style="max-width: 800px; margin: 0 auto;">
    <div style="text-align: center; padding: 2em; background: linear-gradient(135deg, var(--b3-theme-primary), var(--b3-theme-secondary)); border-radius: 12px;">
        <h1 style="color: var(--b3-theme-on-primary); margin: 0; font-size: 2.2em;">🎬 思源媒体播放器</h1>
        <div style="color: var(--b3-theme-on-primary-light); margin-top: 0.5em; font-size: 1.1em;">专业的思源笔记媒体播放插件，集成多平台播放、智能笔记管理与高效学习工具</div>
        <div style="margin-top: 1.5em; display: flex; justify-content: center; gap: 12px; flex-wrap: wrap;">
            <a href="https://github.com/mm-o/siyuan-media-player/blob/main/CHANGELOG.md"
               style="padding: 8px 16px; background: rgba(255,255,255,0.2); color: white; border-radius: 6px; text-decoration: none; font-size: 0.9em;">🗓 更新日志</a>
               <a href="https://github.com/mm-o/siyuan-media-player/issues"
               style="padding: 8px 16px; background: rgba(255,255,255,0.2); color: white; border-radius: 6px; text-decoration: none; font-size: 0.9em;">💬 问题反馈</a>
            <a href="https://vcne5rvqxi9z.feishu.cn/wiki/HOKAw3KTiigaVukvcencOUh7nEb"
               style="padding: 8px 16px; background: rgba(255,255,255,0.2); color: white; border-radius: 6px; text-decoration: none; font-size: 0.9em;">👏 鸣谢</a>
        </div>
    </div>
     <div style="margin-top: 1.5em; padding: 1.5em; background: var(--b3-theme-surface-lighter); border: 1px solid var(--b3-theme-border); border-radius: 8px; box-shadow: 0 4px 12px var(--b3-theme-shadow);">
        <h2 style="color: var(--b3-theme-primary); margin: 0 0 1em; text-align: center; font-size: 1.3em;">🚀 近期更新</h2>

<h3>📅 v0.4.7版本更新 (2025.7.28)</h3>
<h4>🎯 脚本系统重构 - 重新设计扩展脚本加载机制，更符合思源笔记规范</h4>
<div style="padding: 0.8em; background: var(--b3-theme-error-lighter); border: 1px solid var(--b3-theme-error-light); border-radius: 6px; margin: 0.5em 0;">
<strong style="color: var(--b3-theme-error);">⚠️ 本版本已移除第三方相关API，更新后需要安装扩展脚本才能使用第三方功能</strong>
</div>
<p>🔧 <strong>简化扩展架构</strong>: 完全重构扩展系统，直接通过window对象暴露API配置，完全符合思源笔记脚本标准</p>
<p>📜 <strong>扩展脚本升级到v2.0.0</strong>: 极简设计，静默加载，<strong>必须更新到v2.0.0扩展脚本</strong>才能正常使用第三方功能</p>
<p>💡 <strong>安装方法</strong>:</p>
<p>1. 下载扩展脚本文件<br>
2. 思源笔记 → 设置 → 外观 → 代码片段 → JS → 粘贴脚本内容 → 开启<br>
3. 重启思源笔记</p>
<p>📥 <strong>扩展脚本地址</strong>: <a href="https://github.com/mm-o/siyuan-media-player/raw/main/docs/bilibili-extension.js">GitHub</a> | <a href="https://gitee.com/m-o/siyuan-media-player/blob/master/docs/bilibili-extension.js">Gitee</a></p>

<h4>🎯 会员预告</h4>
<div style="padding: 0.8em; background: var(--b3-theme-error-lighter); border: 1px solid var(--b3-theme-error-light); border-radius: 6px; margin: 0.5em 0;">
<strong style="color: var(--b3-theme-error);">⚠️ 下个版本预计上线会员功能，价格会上浮，已打赏用户将获得永久会员资格，请联系我加群，加群请备注打赏订单号，现在打赏也算哦</strong>
</div>

<h4>🎯 交流群</h4>
<p><strong>QQ群</strong>: <a href="https://qm.qq.com/q/wpHDtsfxCw">QQ群</a> QQ群号：306538841</p>

<details>
<summary><strong>📋 查看历史更新</strong></summary>

### 📅 v0.4.6版本更新 (2025.7.25)
#### 🎯 **扩展架构重构** - 插件采用模块化设计，支持可选扩展功能
- 🚨 **重要提示**:
  <div style="padding: 0.8em; background: var(--b3-theme-error-lighter); border: 1px solid var(--b3-theme-error-light); border-radius: 6px; margin: 0.5em 0;">
  <strong style="color: var(--b3-theme-error);">⚠️ 本版本已移除第三方相关API，更新后需要安装扩展脚本才能使用第三方功能</strong>
  </div>
- 💡 **安装方法**:
  1. 下载扩展脚本文件
  2. 思源笔记 → 设置 → 外观 → 代码片段 → JS → 粘贴脚本内容 → 开启
  3. 重启思源笔记
- 📥 **扩展脚本地址**: [GitHub](https://github.com/mm-o/siyuan-media-player/raw/main/docs/bilibili-extension.js) | [Gitee](https://gitee.com/m-o/siyuan-media-player/blob/master/docs/bilibili-extension.js)
#### ✨ **界面优化**
- 🎨 **按钮图标化**: 将文字按钮替换为纯图标，实现极简设计
- ⚡ **服务预初始化**: 解决首次使用时间戳链接无法播放的问题
##### 🔍 **全局搜索功能**
- 🔍 **搜索标签**: 在目录标签前添加搜索按钮，使用🔍字符图标
- 🌐 **全局搜索**: 可搜索所有标签下的所有媒体内容
- ⚡ **实时搜索**: 输入时立即显示搜索结果，支持标题、艺术家、URL搜索
- 🎯 **智能交互**: 点击显示搜索框并自动聚焦，无内容时自动隐藏

### 📅 v0.4.4版本更新 (2025.7.20)
#### 🎴 **笔记卡片可视化系统** - 全新的媒体学习内容管理体验
- 📋 **智能筛选面板**: 在笔记面板中新增媒体类型筛选功能，支持按时间戳、循环片段、截图、媒体卡片四种类型快速筛选查看
- 🖼️ **可视化卡片展示**: 筛选结果以精美卡片形式展示，自动显示封面图/截图、媒体类型、时间信息，让学习内容一目了然
- 🔗 **一键跳转播放**: 点击任意卡片即可直接跳转到对应的媒体时间点开始播放，实现从笔记到媒体的无缝衔接
- ⚡ **实时同步更新**: 在筛选状态下进行时间戳、截图等操作，新内容会立即显示在卡片列表中，保持完美的实时体验
- 🎯 **智能插入定位**: 筛选状态下的所有媒体操作会自动插入到当前文档底部，确保内容有序组织
- 🏷️ **统一属性管理**: 基于v0.4.3版本的自定义属性系统，实现所有媒体类型的统一识别和管理
#### ✨ **播放列表增强功能**
- 🌐 **外部打开支持**: 新增右键菜单"外部打开"功能
  - 扩展/OpenList/WebDAV等网络资源在浏览器中打开
  - 本地文件在资源管理器中定位显示
  - 智能识别资源类型，自动选择最佳打开方式
#### 📱 **移动端支持尝试**
- 🧪 **实验性功能**: 尝试添加移动端本地文件选择支持（可能在某些设备上无法正常工作）
- 🔄 **智能平台检测**: 桌面端使用原生文件对话框，移动端使用HTML5文件选择器
- ⚠️ **注意事项**: 移动端文件选择功能仍在测试阶段，建议优先使用桌面端
#### 🔧 **数据库字段优化**
- 📝 **字段描述管理**: 自动为所有播放列表字段添加描述说明，支持字段重命名后正常加载
  - **字段描述对照表**: 媒体标题、URL、时长、所在标签、来源、类型、艺术家、封面图、艺术家头像、创建时间
  - **故障排除**: 播放列表加载失败时，请检查字段描述是否与上述对照表一致
- 🎯 **映射逻辑优化**: 优先通过描述匹配，确保字段重命名后功能正常
- 🛡️ **智能字段管理**: 避免重复创建，自动补充缺失描述
#### 🔧 **用户体验优化**
- 💡 **数据库配置提示增强**: 优化数据库未配置时的提示体验
  - 使用持久提示替代自动消失提示，需要用户主动关闭
#### 🐛 **缺陷修复**
- ⏱️ **时长显示修复**: 修复本地媒体时长显示问题，现在能正确获取并显示本地视频文件的时长信息
- 🔧 **兼容性提升**: 修复crypto.randomUUID兼容性问题，提升在不同环境下的稳定性
- 🔄 **标签刷新修复**: 修复清空标签（增删媒体）后刷新功能失效的问题，确保根据标签描述信息正确执行智能添加

</details>
    </div>
    <div style="margin-top: 1.5em; padding: 1.5em; background: var(--b3-theme-surface-lighter); border: 1px solid var(--b3-theme-border); border-radius: 8px; box-shadow: 0 4px 12px var(--b3-theme-shadow);">
        <h2 style="color: var(--b3-theme-primary); margin: 0 0 1em; text-align: center; font-size: 1.3em;">🚀 核心功能</h2>
        <ul style="margin: 0; list-style: none; padding: 0;">
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">🎥 <strong>多平台播放</strong> - 本地媒体、扩展媒体、OpenList、WebDAV云存储统一播放</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">⏰ <strong>时间戳跳转</strong> - 精确时间戳链接，一键跳转到指定播放位置</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">🔄 <strong>循环片段</strong> - 自定义循环播放片段，重点内容反复学习</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">📔 <strong>媒体笔记</strong> - 截图、字幕、弹幕导出，支持子文档创建和智能搜索配置，完整的学习笔记生态</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">🤖 <strong>媒体助手</strong> - 字幕列表、弹幕列表、AI媒体总结智能分析</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">📋 <strong>播放列表</strong> - 数据库驱动管理，标签分类、拖拽排序、多视图展示</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">📥 <strong>批量导入</strong> - 一键导入扩展收藏夹、扩展合集、本地文件夹、思源笔记工作空间、OpenList、WebDAV云存储到播放列表</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">📝 <strong>笔记面板</strong> - 独立的文档和块内容查看编辑，支持ID输入、智能标签、右键菜单操作</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">🔗 <strong>智能链接识别</strong> - 数据库URL字段媒体链接直接播放，Ctrl+点击强制浏览器打开</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">⚙️ <strong>设置面板</strong> - 账号配置、播放器设置、通用选项一站式管理</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">🔧 <strong>脚本扩展</strong> - 支持第三方扩展脚本，用户自主选择功能模块</li>
        </ul>
    </div>
    <div style="margin-top: 1.5em; padding: 1.5em; background: var(--b3-theme-surface-lighter); border: 1px solid var(--b3-theme-border); border-radius: 8px; box-shadow: 0 4px 12px var(--b3-theme-shadow);">
        <h2 style="color: var(--b3-theme-primary); margin: 0 0 1em; text-align: center; font-size: 1.3em;">⚠️ 免责说明</h2>
        <ul style="margin: 0;  list-style: none; padding: 0;">
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">🎯 <strong>技术架构</strong> - 插件采用扩展化设计，核心代码专注于播放功能，不包含任何第三方服务代码</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">🎛️ <strong>扩展选择</strong> - 用户可根据需要自主选择是否使用第三方扩展脚本</li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">🛡️ <strong>安全提醒</strong> - 扩展脚本为独立功能模块，请根据个人需求谨慎选择使用</li>
        </ul>
    </div>
    <div style="margin-top: 1.5em; padding: 1.5em; background: var(--b3-theme-surface-lighter); border: 1px solid var(--b3-theme-border); border-radius: 8px; box-shadow: 0 4px 12px var(--b3-theme-shadow);">
        <h2 style="color: var(--b3-theme-primary); margin: 0 0 1em; text-align: center; font-size: 1.3em;">🧧 打赏</h2>
        <p style="margin: 0.5em 0;">如果思源媒体播放器对你有帮助，欢迎给作者点个赞或打赏一杯咖啡，这将鼓励作者持续优化和开发更多实用功能：</p>
        <div style="margin: 1em 0; text-align: center; display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
            <div style="text-align: center;">
                <img src="/plugins/siyuan-media-player/assets/images/alipay.jpg"
                     alt="支付宝付款码"
                     style="width: 280px; border-radius: 8px; box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <p style="margin: 0.5em 0; color: var(--b3-theme-text-lighter);">支付宝</p>
            </div>
            <div style="text-align: center;">
                <img src="/plugins/siyuan-media-player/assets/images/wechat.jpg"
                     alt="微信付款码"
                     style="width: 280px; border-radius: 8px; box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <p style="margin: 0.5em 0; color: var(--b3-theme-text-lighter);">微信</p>
            </div>
        </div>
    </div>
    <div style="margin-top: 1.5em; padding: 1.5em; background: var(--b3-theme-surface-lighter); border: 1px solid var(--b3-theme-border); border-radius: 8px; box-shadow: 0 4px 12px var(--b3-theme-shadow);">
        <h2 style="color: var(--b3-theme-primary); margin: 0 0 1em; text-align: center; font-size: 1.3em;">📖 使用指南</h2>
        <ul style="margin: 0;  list-style: none; padding: 0;">
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🗄️ <strong>绑定数据库</strong> - 配置播放列表数据存储</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 选择一个数据库块，点击数据库图标或者右键 > 复制 > 复制 ID<br>
                2. 打开思源媒体播放器设置面板<br>
                3. 在"通用"标签找到"播放列表数据库"<br>
                4. 粘贴步骤一复制的数据库块ID，绑定数据库<br>
                5. 绑定成功后，所有播放列表数据将同步到指定数据库<br>
                6. 支持自动创建画廊视图，方便直观管理媒体列表
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🔧 <strong>配置扩展账号</strong> - 登录扩展服务访问个人资源</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 在设置面板中找到"扩展账号"部分<br>
                2. 点击"登录扩展账号"按钮<br>
                3. 扫描显示的二维码（使用扩展服务手机APP）<br>
                4. 登录成功后可观看扩展视频并批量添加收藏夹
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🔗 <strong>配置OpenList服务</strong> - 连接云存储服务器</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 在设置面板中找到"OpenList配置"部分<br>
                2. 填写OpenList服务器地址（如：http://localhost:5244）<br>
                3. 输入用户名和密码<br>
                4. 配置成功后可直接浏览和播放OpenList中的媒体文件
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">☁️ <strong>配置WebDAV云存储</strong> - 连接WebDAV服务</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 在设置面板中找到"WebDAV配置"部分<br>
                2. 填写WebDAV服务器地址<br>
                3. 输入用户名和密码<br>
                4. 支持坚果云、NextCloud等主流WebDAV服务<br>
                5. 配置成功后可直接浏览和播放WebDAV中的媒体文件
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">📁 <strong>添加本地媒体</strong> - 支持多选</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 直接点击播放列表底部部的"添加"按钮<br>
                2. 在弹出的文件管理器选择需要添加的媒体<br>
                3. 点击打开就可以添加到播放列表<br>
                4. 支持单选和多选<br>
                5. 系统会自动检测同名字幕文件
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">📂 <strong>添加本地文件夹</strong> - 批量导入本地文件</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 点击播放列表标签“+”弹出菜单<br>
                2. 点击“添加本地文件夹”<br>
                3. 在弹出的文件浏览器选择需要添加的文件夹<br>
                4. 点击“选择文件夹”按钮<br>
                5. 系统将自动扫描并批量导入所有符合条件的媒体文件
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🏠 <strong>添加思源空间媒体</strong> - 浏览工作空间文件</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 点击播放列表标签“+”弹出菜单<br>
                2. 点击“添加添加思源空间”即可添加思源工作空间到播放列表<br>
                5. 思源空间的媒体使用相对路径，便于工作空间迁移
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🔗 <strong>添加OpenList媒体</strong> - 云存储流式播放</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 确保已配置OpenList服务器连接<br>
                2. 点击播放列表标签“+”弹出菜单<br>
                3. 点击“添加OpenList云盘”<br>
                4. 媒体将通过流式播放，无需下载到本地
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">☁️ <strong>添加WebDAV媒体</strong> - 云盘直接播放</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 确保已配置WebDAV云存储连接<br>
                2. 点击播放列表标签“+”弹出菜单<br>
                3. 点击“添加WebDAV云盘”<br>
                4. 媒体将通过流式播放，无需下载到本地
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">📥 <strong>批量导入扩展收藏夹</strong> - 一键导入收藏视频</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 确保已登录扩展账号<br>
                2. 点击播放列表标签“+”弹出菜单
                3. 点击“添加扩展收藏夹”<br>
                3. 选择要导入的收藏夹<br>
                4. 系统将批量导入收藏夹中的所有视频
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">📚 <strong>批量导入扩展合集</strong> - 一键导入合集视频</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 确保已登录扩展账号<br>
                2. 点击播放列表标签"+"弹出菜单<br>
                3. 点击"添加扩展合集"<br>
                4. 输入合集中任意视频的链接<br>
                5. 按回车确认，系统将自动获取整个合集并批量导入所有视频
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🏷️ <strong>标签管理</strong> - 创建和管理播放列表标签</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 在播放列表顶部点击"+"按钮创建新标签<br>
                2. 输入标签名称并确认<br>
                3. <strong>重命名标签</strong>：右键点击标签 > 选择"重命名"<br>
                4. <strong>删除标签</strong>：右键点击标签 > 选择"删除"即可删除标签及标签下媒体<br>
                5. <strong>清空标签</strong>：右键点击标签 > 选择"清空"即可清空标签下媒体
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🔄 <strong>智能刷新</strong> - 自动同步文件夹、扩展收藏夹、扩展合集</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 右键点击需要刷新的标签<br>
                2. 选择"刷新标签"<br>
                3. 系统将根据标签类型自动执行相应的刷新策略：<br>
                • <strong>文件夹标签</strong>：检测本地文件夹变化，智能增删媒体项<br>
                • <strong>扩展收藏夹</strong>：检测收藏夹内容变化，保持数据一致性<br>
                • <strong>扩展合集</strong>：检测合集内容变化，自动同步最新视频<br>
                4. 智能对比现有数据，仅处理变化项目，性能提升显著
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🔄 <strong>拖拽操作</strong> - 媒体移动和排序</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                <strong>拖拽移动媒体：</strong><br>
                1. <strong>在标签间移动</strong>：将媒体项从一个标签拖拽到另一个标签<br>
                <strong>拖拽排序：</strong><br>
                1. <strong>媒体排序</strong>：在同一标签内拖拽媒体项调整播放顺序<br>
                2. <strong>标签排序</strong>：拖拽标签头部调整标签的显示顺序<br>
                3. 排序后的顺序会自动保存到数据库
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">📝 <strong>笔记集成</strong> - 截图、时间戳、循环片段和媒体笔记</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                <strong>创建截图（带时间戳）：</strong><br>
                1. 播放视频到需要截图的位置<br>
                2. 点击截图按钮创建截图<br>
                3. 截图会根据设置是否带时间戳<br><br>
                <strong>创建时间戳和循环片段：</strong><br>
                1. 播放视频到需要标记的位置<br>
                2. 点击时间戳按钮创建时间戳链接<br>
                3. <strong>创建循环片段</strong>：<br>
                   - 点击循环片段按钮设置起点<br>
                   - 播放到结束位置再次点击设置终点<br>
                   - 可设置循环次数和循环后是否暂停<br>
                4. 生成的链接会根据设置插入到指定位置或复制到剪贴板<br><br>
                <strong>创建媒体笔记：</strong><br>
                1. 播放要记录笔记的媒体<br>
                2. 点击控制栏中的"媒体笔记"按钮（或使用快捷键）<br>
                3. 系统根据自定义模板创建笔记，包含媒体信息、时间戳、缩略图等<br>
                4. 笔记可插入当前文档、创建到指定笔记本或在指定文档下创建子文档<br>
                5. <strong>智能搜索配置</strong>：在设置中输入文档名称或笔记本名称进行搜索，快速定位目标位置<br>
                6. <strong>子文档创建</strong>：选择父文档后，系统将在其下自动创建子文档，保持笔记结构的层次性
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">📝 <strong>笔记面板</strong> - 文档和块内容查看编辑</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 点击思源媒体播放器面板顶部的"笔记"标签<br>
                2. <strong>添加笔记标签</strong>：点击"+"按钮，输入文档ID或块ID<br>
                3. <strong>查看内容</strong>：点击标签查看对应的文档或块内容<br>
                4. <strong>右键操作</strong>：右键点击标签可进行以下操作：<br>
                   - <strong>重命名</strong>：修改标签显示名称（最多4个字符）<br>
                   - <strong>在思源中打开</strong>：直接跳转到思源中的对应位置<br>
                   - <strong>复制ID</strong>：复制文档或块ID到剪贴板<br>
                   - <strong>删除标签</strong>：移除不需要的笔记标签<br>
                5. <strong>完整编辑</strong>：支持在面板中直接编辑文档内容，与思源编辑器功能一致
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🧠 <strong>媒体助手（Pro版）</strong> - 字幕、弹幕、总结</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 在播放视频时点击"媒体助手"按钮<br>
                2. <strong>字幕列表</strong>：浏览、搜索字幕内容，点击跳转<br>
                3. <strong>视频摘要</strong>：查看AI生成的内容概要<br>
                4. <strong>弹幕列表</strong>：浏览视频弹幕列表，需要在设置中开启<br>
                5. <strong>一键导出</strong>：将助手内容导出到笔记
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">⌨️ <strong>快捷键设置</strong> - 配置自定义快捷键</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                1. 打开思源设置 > 快捷键<br>
                2. 搜索"媒体播放器"或"siyuan-media-player"<br>
                3. 为以下功能设置快捷键：<br>
                   - <strong>⏱️ 添加时间戳</strong>：快速生成当前时间链接<br>
                   - <strong>🔄 循环片段</strong>：设置循环播放区间<br>
                   - <strong>📸 截图</strong>：捕获视频画面<br>
                   - <strong>📔 媒体笔记</strong>：快速创建笔记<br>
                   - <strong>🧠 打开媒体播放器面板</strong>：打开/关闭dock面板<br>
                4. <strong>播放器内置快捷键</strong>：<br>
                   - 空格：播放/暂停<br>
                   - 左右箭头：快退/快进<br>
                   - 上下箭头：音量调节<br>
                   - <strong>Ctrl+↑</strong>：增加播放速度（+0.5x，最高5x）<br>
                   - <strong>Ctrl+↓</strong>：减少播放速度（-0.5x，最低0.5x）
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🎵 <strong>播放控制</strong> - 基本播放和循环功能</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                <strong>基本播放操作：</strong><br>
                1. <strong>播放/暂停</strong>：点击播放按钮或按空格键<br>
                2. <strong>进度控制</strong>：拖拽进度条或使用左右箭头键<br>
                3. <strong>音量调节</strong>：使用音量滑块或上下箭头键<br>
                4. <strong>播放速度</strong>：在设置中调整播放速度，或使用Ctrl+↑/↓快捷键<br>
                5. <strong>画中画模式</strong>：在播放器设置中开启，或选择"画中画"打开方式<br>
                6. <strong>全屏播放</strong>：点击全屏按钮或双击播放区域<br><br>
                <strong>循环播放功能：</strong><br>
                1. <strong>单项循环</strong>：重复播放当前媒体<br>
                2. <strong>列表循环</strong>：播放完列表后重新开始<br>
                3. <strong>片段循环</strong>：设置特定时间段重复播放<br>
                4. <strong>循环次数设置</strong>：在设置中配置循环次数<br>
                5. <strong>循环后暂停</strong>：可设置循环结束后自动暂停
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">🔗 <strong>自定义链接格式</strong> - 个性化时间戳链接</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                在设置中，您可以自定义时间戳链接的显示格式。例如：<br>
                <code>- [😄标题 时间 字幕](链接)</code> // 带有表情符号的链接<br>
                <code>> 🕒 时间 | 标题 | 字幕</code> // 引用格式的链接
                </div>
                </details>
            </li>
            <li style="margin: 0.5em 0; padding: 10px 14px; background: var(--b3-theme-surface); border-radius: 8px; border-left: 4px solid var(--b3-theme-primary); box-shadow: 0 2px 4px var(--b3-theme-shadow-light);">
                <details>
                <summary style="color: var(--b3-theme-primary); cursor: pointer; font-weight: 500; font-size: 1.05em;">📝 <strong>自定义媒体笔记模板</strong> - 个性化笔记格式</summary>
                <div style="margin-top: 0.8em; padding-top: 0.8em; border-top: 1px solid var(--b3-theme-border);">
                您可以在设置中创建自己的媒体笔记模板，支持各种变量：<br>
                <strong>可用变量</strong>：媒体标题、当前时间戳、艺术家名称、媒体URL、媒体时长、媒体缩略图、媒体类型、媒体ID、当前日期、当前日期和时间<br><br>
                <strong>目标配置</strong>：<br>
                • <strong>智能搜索</strong>：输入关键字搜索文档和笔记本，快速定位目标位置<br>
                • <strong>子文档创建</strong>：选择父文档后，媒体笔记将作为子文档创建，保持层次结构<br>
                • <strong>便捷设置</strong>：常用配置可保存，简化后续创建流程
                </div>
                </details>
            </li>
        </ul>
    </div>
</div>
